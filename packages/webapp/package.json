{"name": "daiquiri-webapp", "version": "1.0.0", "description": "", "main": "index.js", "private": true, "scripts": {"build:widget": "webpack --config webpack.config.js", "dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix"}, "dependencies": {"@activatestudio/spritz-ui-components": "^0.0.4", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/icons-material": "^5.15.15", "@mui/material": "^5.15.15", "@sentry/nextjs": "^7.99.0", "antd": "^5.15.3", "axios": "^1.6.7", "babel-eslint": "^10.1.0", "bootstrap": "^5.3.2", "bootstrap-icons": "^1.11.3", "dompurify": "^3.0.6", "dotenv": "^16.4.1", "eslint-plugin-jsdoc": "^48.0.6", "jsonwebtoken": "^9.0.2", "next": "14.1.0", "react": "^18", "react-bootstrap": "^2.10.1", "react-dom": "^18", "react-hot-toast": "^2.4.1", "socket.io-client": "^4.7.5"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/preset-env": "^7.24.7", "@babel/preset-react": "^7.24.7", "@types/node": "20.11.16", "@types/react": "18.2.53", "babel-loader": "^9.1.3", "css-loader": "^7.1.2", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "style-loader": "^4.0.0", "webpack": "^5.99.9", "webpack-cli": "^5.1.4"}}
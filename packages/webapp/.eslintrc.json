{"env": {"browser": true, "es2021": true, "node": true, "commonjs": true, "shared-node-browser": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:react-hooks/recommended"], "parserOptions": {"ecmaVersion": 12, "sourceType": "module", "ecmaFeatures": {"jsx": true}}, "plugins": ["react", "react-hooks"], "rules": {"semi": ["error", "always"], "indent": ["error", 2], "react/no-unescaped-entities": "off"}}
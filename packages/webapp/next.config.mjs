/** @type {import('next').NextConfig} */

const nextConfig = {
  basePath: '',
  compress: false,
  poweredByHeader: false,
  reactStrictMode: true,
  trailingSlash: false,
  eslint: {
    dirs: ['src'],
    ignoreDuringBuilds: true,
  },
  env: {
    API_BASE_URL: 'https://api.dev.daiquiri.activate.bar',
    SPRITZ_API_URL: 'https://api.dev.spritz.cafe',
    SPRITZ_API_TOKEN:
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhZG1pbnw2NGMyNzE2NGJhMWEzNGUzZjBkZWE1OWEiLCJpYXQiOjE3MjEyOTAzMzMsImV4cCI6MTg3ODk3MDMzM30.VeANMoE3WVMf0OmqXLtgIwub-GfyOxQ8XC9i6oUs2g4',
    AGENT_ID: '664326cfa20ccf48d784fb70',
    ORGANIZATION_ID: '6672baa6ccf533e516f4a3c5',
    CALL_BACK_URL: 'https://api.dev.daiquiri.activate.bar/v1/chat/agent',
    WIDGET: 'no',
  },
  async redirects() {
    return [
      {
        source: '/chat/:chatBox', // Adjust path pattern as needed
        destination: '/[chatBox]', // Adjust dynamic route pattern
        permanent: true,
      },
    ];
  },
};

export default nextConfig;

const path = require('path');
const webpack = require('webpack');

module.exports = {
  entry: './src/components/widget/widget.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'page.bundle.js',
    library: 'MyWidget',
    libraryTarget: 'umd',
    globalObject: 'this', // Ensure it works in different environments
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader',
          options: {
            presets: ['@babel/preset-env', '@babel/preset-react'],
          },
        },
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader'],
      },
    ],
  },
  resolve: {
    extensions: ['.js', '.jsx'],
    alias: {
      '@': path.resolve(__dirname, 'src'),
    },
    fallback: {
      http: false, // Ignore http module
    },
  },
  mode: 'development', // or 'production'
};

import { postAPI, getAPI } from './wrapper';

const APIEndPoint = '/v1/chat';

export const ChatAPI = {
  async startChat(data = {}) {
    const url = `${APIEndPoint}`;
    try {
      return await postAPI(url, data);
    } catch (error) {
      console.error('Error fetching chat:', error);
      return [];
    }
  },
  async getChat(organizationId, rootId) {
    const url = `${APIEndPoint}/${rootId}/${organizationId}`;
    try {
      return await getAPI(url);
    } catch (error) {
      console.error('Error fetching chat:', error);
      return [];
    }
  },
};

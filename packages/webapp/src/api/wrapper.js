// apiWrapper.js
import axios from 'axios';
import { isEmpty } from 'lodash';
import config from '../helper/config.json';

const api = axios.create({
  baseURL: config.envVariables.API_BASE_URL,
});
const request = async (
  method,
  url,
  data = null,
  params = null,
  header = {}
) => {
  try {
    const headers = {
      accept: 'application/json',
    };
    if (!isEmpty(header)) {
      Object.keys(header).map((key) => {
        headers[key] = header[key];
      });
    }
    const response = await api.request({
      method,
      url: url,
      data,
      params,
      headers,
    });
    return response.data;
  } catch (error) {
    if (error.response) {
      // The request was made and the server responded with a status code
      console.error('Error response:', error.response.data);
      console.error('Status code:', error.response.status);
      console.error('Headers:', error.response.headers);
    } else if (error.request) {
      console.error('No response received:', error.request);
    } else {
      console.error('Error setting up the request:', error.message);
    }
    return error;
  }
};

export const getAPI = (url, params = null, header) =>
  request('get', url, null, params, header);
export const postAPI = (url, data = null, params = null) =>
  request('post', url, data, params);
export const put = (url, data = null, params = null) =>
  request('put', url, data, params);
export const patchAPI = (url, data = null, params = null) =>
  request('patch', url, data, params);
export const deleteAPI = (url, params = null) =>
  request('delete', url, null, params);

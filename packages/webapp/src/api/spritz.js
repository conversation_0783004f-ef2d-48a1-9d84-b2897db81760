import axios from 'axios';
import config from '../helper/config.json';
const APIEndPoint = config.envVariables.SPRITZ_API_URL;
const token = config.envVariables.SPRITZ_API_TOKEN;

export const SpritzAPI = {
  async getAgents(organizationId) {
    try {
      if (!organizationId) {
        throw new Error('ORGANIZATION_ID is not defined');
      }
      if (!token) {
        throw new Error('SPRITZ_API_TOKEN is not defined');
      }
      const url = `${APIEndPoint}/v1/organization/list?_id=${organizationId}`;
      const response = await axios.get(url, {
        headers: {
          accept: 'application/json',
          Authorization: token,
        },
      });
      const data = response.data?.data || [];
      return data;
    } catch (error) {
      console.error('Error:', error.message);
      throw error; // Optional: rethrow the error if you want to handle it further up the call stack
    }
  },
};

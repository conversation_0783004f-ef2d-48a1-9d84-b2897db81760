import socketIOClient from 'socket.io-client';
import errorList from '../helper/errorList.json';
import config from '../helper/config.json';

const socketRef = {
  current: null,
};

// Connect to the socket server
export const connectSocket = (url) => {
  socketRef.current = socketIOClient(url);
  return socketRef.current;
};

// Join a specific room using the room ID
export const joinRoom = (roomId) => {
  if (socketRef.current) {
    socketRef.current.emit('joinRoom', roomId);
  }
};

// Emit a message with a specific event name, message data, and optional callback
export const emitMessage = async (
  event,
  message,
  roomId,
  initializeSocket,
  callback
) => {
  if (socketRef.current) {
    const flag = socketRef.current.emit(event, message, callback);
    if (!flag.connected) {
      initializeSocket(roomId, true);
      socketRef.current.emit(event, message, callback);
    }
  }
};

// Register a handler for a specific event
export const onMessage = (event, callback) => {
  if (socketRef.current) {
    socketRef.current.on(event, callback);
  }
};

// Disconnect the socket
export const disconnectSocket = () => {
  if (socketRef.current) {
    socketRef.current.disconnect();
  }
};

// Emit a heartbeat event
export const emitHeartbeat = () => {
  if (socketRef.current) {
    socketRef.current.emit('heartbeat');
  }
};

// Attach heartbeat and reconnection logic
export const setupHeartbeatAndReconnect = (setError, widgetCallBack) => {
  let retryCount = 0;
  const maxRetries = 5;

  const reconnect = () => {
    if (retryCount < maxRetries) {
      retryCount++;
      connectSocket(config.envVariables.API_BASE_URL);
    } else {
      if (widgetCallBack) {
        widgetCallBack(errorList.serverNotResponding, null);
      }
      setError('Unable to reconnect to the server.');
    }
  };

  const handleDisconnect = () => {
    if (widgetCallBack) {
      widgetCallBack(errorList.socketConnectionBreaks, null);
    }
    setError('Socket connection lost. Attempting to reconnect...');
    reconnect();
  };

  const handleHeartbeatResponse = () => {
    // Handle successful heartbeat response
    setError(null); // Clear any previous errors
  };

  const heartbeatInterval = setInterval(() => {
    emitHeartbeat();
  }, 10000); // Adjust interval as needed

  return { handleDisconnect, handleHeartbeatResponse, heartbeatInterval };
};

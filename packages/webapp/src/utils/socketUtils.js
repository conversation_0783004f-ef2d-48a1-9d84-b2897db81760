import socketIOClient from 'socket.io-client';
import errorList from '../helper/errorList.json';
import config from '../helper/config.json';
import logger from './logger';

const socketRef = {
  current: null,
  isConnecting: false,
  connectionAttempts: 0,
  maxConnectionAttempts: 5,
  roomId: null,
};

// Connect to the socket server
export const connectSocket = (url) => {
  // Prevent multiple simultaneous connection attempts
  if (socketRef.isConnecting) {
    logger.info('Socket connection already in progress');
    return socketRef.current;
  }

  // If already connected and socket is healthy, return existing connection
  if (socketRef.current && socketRef.current.connected) {
    logger.info('Using existing socket connection');
    return socketRef.current;
  }

  socketRef.isConnecting = true;
  socketRef.connectionAttempts++;

  try {
    logger.info(`Attempting socket connection to: ${url} (attempt ${socketRef.connectionAttempts})`);

    socketRef.current = socketIOClient(url, {
      timeout: config.timeouts?.SOCKET_CONNECTION_TIMEOUT || 20000,
      reconnection: true,
      reconnectionDelay: config.timeouts?.SOCKET_RECONNECTION_DELAY || 1000,
      reconnectionDelayMax: config.timeouts?.SOCKET_RECONNECTION_DELAY_MAX || 5000,
      maxReconnectionAttempts: config.timeouts?.SOCKET_MAX_RECONNECTION_ATTEMPTS || 5,
      transports: ['websocket', 'polling'],
      forceNew: false,
    });

    // Set up connection event handlers
    socketRef.current.on('connect', () => {
      logger.info('Socket connected successfully');
      socketRef.isConnecting = false;
      socketRef.connectionAttempts = 0;

      // Rejoin room if we have a stored roomId
      if (socketRef.roomId) {
        logger.info(`Rejoining room: ${socketRef.roomId}`);
        socketRef.current.emit('joinRoom', socketRef.roomId);
      }
    });

    socketRef.current.on('disconnect', (reason) => {
      logger.warn(`Socket disconnected: ${reason}`);
      socketRef.isConnecting = false;
    });

    socketRef.current.on('connect_error', (error) => {
      logger.error('Socket connection error:', error);
      socketRef.isConnecting = false;
    });

    return socketRef.current;
  } catch (error) {
    logger.error('Failed to create socket connection:', error);
    socketRef.isConnecting = false;
    throw error;
  }
};

// Join a specific room using the room ID
export const joinRoom = (roomId) => {
  if (!roomId) {
    logger.error('Cannot join room: roomId is required');
    return;
  }

  // Store roomId for reconnection scenarios
  socketRef.roomId = roomId;

  if (socketRef.current && socketRef.current.connected) {
    logger.info(`Joining room: ${roomId}`);
    socketRef.current.emit('joinRoom', roomId);
  } else {
    logger.warn(`Socket not connected, will join room ${roomId} after connection`);
  }
};

// Emit a message with a specific event name, message data, and optional callback
export const emitMessage = async (
  event,
  message,
  roomId,
  initializeSocket,
  callback
) => {
  if (socketRef.current) {
    const flag = socketRef.current.emit(event, message, callback);
    if (!flag.connected) {
      initializeSocket(roomId, true);
      socketRef.current.emit(event, message, callback);
    }
  }
};

// Register a handler for a specific event
export const onMessage = (event, callback) => {
  if (socketRef.current) {
    socketRef.current.on(event, callback);
  }
};

// Disconnect the socket
export const disconnectSocket = () => {
  if (socketRef.current) {
    socketRef.current.disconnect();
  }
};

// Emit a heartbeat event
export const emitHeartbeat = () => {
  if (socketRef.current) {
    socketRef.current.emit('heartbeat');
  }
};

// Attach heartbeat and reconnection logic
export const setupHeartbeatAndReconnect = (setError, widgetCallBack) => {
  let retryCount = 0;
  const maxRetries = 5;

  const reconnect = () => {
    if (retryCount < maxRetries) {
      retryCount++;
      connectSocket(config.envVariables.API_BASE_URL);
    } else {
      if (widgetCallBack) {
        widgetCallBack(errorList.serverNotResponding, null);
      }
      setError('Unable to reconnect to the server.');
    }
  };

  const handleDisconnect = () => {
    if (widgetCallBack) {
      widgetCallBack(errorList.socketConnectionBreaks, null);
    }
    setError('Socket connection lost. Attempting to reconnect...');
    reconnect();
  };

  const handleHeartbeatResponse = () => {
    // Handle successful heartbeat response
    setError(null); // Clear any previous errors
  };

  const heartbeatInterval = setInterval(() => {
    emitHeartbeat();
  }, 10000); // Adjust interval as needed

  return { handleDisconnect, handleHeartbeatResponse, heartbeatInterval };
};

const setLocalStorageItem = (key, value) => {
  try {
    // Convert non-string values to JSON format
    const serializedValue = JSON.stringify(value);
    localStorage.setItem(key, serializedValue);
  } catch (error) {
    console.error(`Error setting ${key} in localStorage: ${error.message}`);
  }
};

const getLocalStorageItem = (key) => {
  try {
    // Get the serialized value from localStorage
    const serializedValue =
      typeof window !== 'undefined' && localStorage.getItem(key);
    // Parse the JSON string to get the original value
    let value = {};
    if (serializedValue !== null) {
      value = JSON.parse(serializedValue);
    }
    return value;
  } catch (error) {
    console.error(
      `Error retrieving ${key} from localStorage: ${error.message}`
    );
    return null; // Return null or any default value in case of an error
  }
};

module.exports = {
  setLocalStorageItem,
  getLocalStorageItem,
};

/**
 * Client-side logging utility for debugging communication issues
 */

const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3,
};

class Logger {
  constructor() {
    this.level = process.env.NODE_ENV === 'development' ? LOG_LEVELS.DEBUG : LOG_LEVELS.INFO;
    this.logs = [];
    this.maxLogs = 100; // Keep last 100 logs in memory
  }

  _log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level: Object.keys(LOG_LEVELS)[level],
      message,
      data,
    };

    // Add to in-memory logs
    this.logs.push(logEntry);
    if (this.logs.length > this.maxLogs) {
      this.logs.shift();
    }

    // Console output based on level
    if (level <= this.level) {
      const logMethod = level === LOG_LEVELS.ERROR ? 'error' : 
                       level === LOG_LEVELS.WARN ? 'warn' : 
                       level === LOG_LEVELS.INFO ? 'info' : 'debug';
      
      const prefix = `[DAIQUIRI-${logEntry.level}] ${timestamp}`;
      
      if (data) {
        console[logMethod](prefix, message, data);
      } else {
        console[logMethod](prefix, message);
      }
    }

    // Store critical errors in localStorage for debugging
    if (level === LOG_LEVELS.ERROR) {
      try {
        const errorLogs = JSON.parse(localStorage.getItem('daiquiri_error_logs') || '[]');
        errorLogs.push(logEntry);
        
        // Keep only last 20 error logs
        if (errorLogs.length > 20) {
          errorLogs.shift();
        }
        
        localStorage.setItem('daiquiri_error_logs', JSON.stringify(errorLogs));
      } catch (e) {
        console.error('Failed to store error log:', e);
      }
    }
  }

  error(message, data = null) {
    this._log(LOG_LEVELS.ERROR, message, data);
  }

  warn(message, data = null) {
    this._log(LOG_LEVELS.WARN, message, data);
  }

  info(message, data = null) {
    this._log(LOG_LEVELS.INFO, message, data);
  }

  debug(message, data = null) {
    this._log(LOG_LEVELS.DEBUG, message, data);
  }

  // Get recent logs for debugging
  getRecentLogs(count = 50) {
    return this.logs.slice(-count);
  }

  // Get error logs from localStorage
  getErrorLogs() {
    try {
      return JSON.parse(localStorage.getItem('daiquiri_error_logs') || '[]');
    } catch (e) {
      console.error('Failed to retrieve error logs:', e);
      return [];
    }
  }

  // Clear all logs
  clearLogs() {
    this.logs = [];
    try {
      localStorage.removeItem('daiquiri_error_logs');
    } catch (e) {
      console.error('Failed to clear error logs:', e);
    }
  }

  // Export logs for debugging
  exportLogs() {
    const allLogs = {
      recentLogs: this.getRecentLogs(),
      errorLogs: this.getErrorLogs(),
      timestamp: new Date().toISOString(),
    };
    
    return JSON.stringify(allLogs, null, 2);
  }
}

// Create singleton instance
const logger = new Logger();

export default logger;

// Convenience exports
export const logError = (message, data) => logger.error(message, data);
export const logWarn = (message, data) => logger.warn(message, data);
export const logInfo = (message, data) => logger.info(message, data);
export const logDebug = (message, data) => logger.debug(message, data);

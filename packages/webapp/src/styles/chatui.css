.chat-container {
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1 0 0;
  align-self: stretch;
  height: calc(
    100vh - 65px
  ); /* Ensure the container takes up the full viewport height */
  position: relative;
}

p {
  margin: 0 !important;
}

.chat-header {
  width: 100%;
  background: #5379ff;
  color: #fff;
  text-align: center;
  padding: 10px 0;
  position: sticky;
  top: 0;
  z-index: 1;
}

.message p {
  margin: 0 0 8px 0;  /* bottom margin for paragraphs */
}

/* Allow line breaks inside message */
.message br {
  display: block;
  margin-bottom: 8px;
}

/* Bold, italics, underline supported naturally */
/* Just a minimal reset for better rendering */
.message strong, .message b {
  font-weight: 700;
}

.message em, .message i {
  font-style: italic;
}

.message u {
  text-decoration: underline;
}

/* Links inside messages */
.message a {
  color: #1a73e8; /* blue-ish link color */
  text-decoration: underline;
  cursor: pointer;
}

.message a:hover, .message a:focus {
  color: #1558b0;
  text-decoration: none;
}

/* Lists inside messages */
.message ul, .message ol {
  margin: 8px 0;
  padding-left: 20px;
}

.message li {
  margin-bottom: 4px;
}

/* Images inside messages */
.message img {
  max-width: 100%;
  height: auto;
  border-radius: 12px;
  display: block;
  margin: 8px 0;
}

/* Tooltip should still appear correctly */
.message:hover .tooltip {
  opacity: 1;
  pointer-events: auto;
}

/* Optional: handle code blocks or inline code */
.message code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 13px;
}

/* To handle multiline preformatted text */
.message pre {
  background-color: #f0f0f0;
  padding: 10px;
  border-radius: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 13px;
  margin: 8px 0;
}

.chat-body {
  width: 100%;
  flex: 1;
  overflow-y: auto;
  padding: 25px;
  margin-bottom: 30px;
}

.message-container.sent {
  display: flex;
  padding: 6px 6px;
  justify-content: flex-end;
  align-items: flex-end;
  animation: messageIn 0.6s ease-out;
  gap: 10px;
  margin-left: 80px;
}

.message-container.received {
  display: block;
  padding: 6px 6px;
  justify-content: flex-start;
  animation: messageIn 0.6s ease-out;
  align-items: flex-end;
  margin-right: 80px;
  /* gap: 10px; */
}

.message.sent {
  padding: 12px 24px;
  border-radius: 24px;
  background: var(--Primary-1000, #5379ff);
  color: var(--Basic-White, #fff);
  /* font-family: Inter; */
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  overflow-wrap: break-word;  /* Handle long words or links */
  white-space: normal;         /* Allow wrapping */
  word-break: break-word;      /* Prevent overflow */
  /* padding: 10px; */
}

.tooltip {
  position: absolute;
  top: -10px; /* Adjust to position above the message div */
  right: 0; /* Align to the right */
  transform: translateY(-100%); /* Adjust to position correctly */
  background-color: #555;
  color: #fff;
  text-align: center;
  border-radius: 6px;
  padding: 5px 10px;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s;
  cursor: pointer;
  white-space: nowrap;
  font-size: 12px;
}

.message:hover .tooltip {
  opacity: 1;
}

.message.received {
  position: relative; /* Ensure the tooltip positions relative to the message div */
  display: inline-block;
  border-radius: 24px;
  background: var(--Basic-Off-White, #ebe8e8);
  color: #000;
  /* font-family: Inter; */
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  /* overflow: hidden; */
  text-overflow: ellipsis;
  padding: 12px 24px;
  overflow-wrap: break-word;  /* Handle long words or links */
  white-space: normal;         /* Allow wrapping */
  word-break: break-word;      /* Prevent overflow */
  /* padding: 10px !important; */
}

.message-header {
  background-color: #c4c4c4;
  display: flex;
  justify-content: end;
  /* align-items: center; */
  width: 100%; /* Same width as the message div */
  /* border-top-right-radius: 24px;
  border-top-left-radius: 24px; */
}

.header-button {
  /* border-top-right-radius: 24px; */
  background-color: transparent;
  color: var(--Basic-White, rgb(0, 0, 0));
  border: none;
  padding: 4px 8px;
  /* border-radius: 4px; */
  cursor: pointer;
  align-self: flex-end;
}

.copy-button {
  /* border-top-right-radius: 24px; */
  background-color: transparent;
  color: var(--Basic-White, rgb(0, 0, 0));
  /* border: none; */
  padding: 4px 8px;
  /* border-radius: 4px; */
  cursor: pointer;
  align-self: flex-end;
  border-right: 1px solid grey;
  border-left: 1px solid grey;
  border-top: none;
  border-bottom: none;
}

.header-button:hover {
  /* background-color: #415ebc; */
}
.chat-footer {
  width: 100%;
  display: flex;
  padding: 10px 24px;
  background: #fff;
  border-top: 1px solid #ddd;
  position: sticky;
  bottom: 0;
  z-index: 1;
}

.dropdown-up .dropdown-menu {
  top: auto;
  bottom: 100%;
}

.custom-dropdown-toggle {
  /* margin-right: 12px !important; */
  color: #000 !important;
  background-color: transparent !important; /* Change button background color */
  border-radius: 6px !important;
  border: 1px solid var(--Greyscale-100, rgba(12, 15, 19, 0.1)) !important;
  padding: 8px 20px !important; /* Optional: Add padding */
}

.custom-dropdown-toggle:hover,
.custom-dropdown-toggle:focus,
.custom-dropdown-toggle:active {
  color: #000 !important;
  background-color: transparent !important; /* Change button background color */
  border-radius: 6px !important;
  border-color: 1px solid var(--Greyscale-100, rgba(12, 15, 19, 0.1)) !important; /* Change border color on hover/focus/active */
}

.custom-dropdown-toggle .agent-name {
  margin-right: 25px !important; /* Add margin between text and arrow icon */
}

.dropdown-icon {
  background-color: orange;
  width: 1.5rem;
  height: 1.5rem;
  margin-bottom: -5px;
  margin-right: 15px !important;
}

#message-input {
  overflow-y: auto;
  flex: 1;
  margin-right: 12px;
  background-color: #f5f4f4;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  outline: none;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
  resize: none; /* Prevents manual resizing */
  max-height: 150px; /* Maximum height before scrolling */
  overflow-y: auto; /* Enable vertical scrolling */
  line-height: 1; /* Adjusts the line height */
  padding: 12px 10px; /* Adjusts padding for vertical alignment */
}

.sendButton {
  display: flex;
  padding: 8px 24px !important;
  align-items: flex-start;
  gap: 12px;
  max-height: 40px;
  border-radius: 6px;
  /* opacity: 0.4; */
  background: var(--Primary-1000, #5379ff);
  color: #fff;
  border: none;
  cursor: pointer;
}

/* chatui.css */
.typing-indicator-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  padding: 10px;
  position: absolute;
  bottom: 60px; /* Adjust based on the height of the footer */
}

.typing-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 30px;
  border-radius: 24px;
  color: #000;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  border-radius: 15px;
  animation: messageIn 0.6s ease-out;
}

.typing-indicator p {
  color: #000;
}

.typing-dots {
  display: flex;
  align-items: center;
  margin-left: 5px;
}

.typing-dots span {
  display: inline-block;
  width: 6px;
  height: 6px;
  margin: 0 2px;
  background-color: #000;
  border-radius: 50%;
  animation: typingDots 1.4s infinite ease-in-out both;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-dots span:nth-child(3) {
  animation-delay: 0s;
}

@keyframes typingDots {
  0%,
  80%,
  100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes messageIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.typingIndicatorBubble {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 52px;
  height: 40px;
  margin: 0px 8px;
  border-radius: 12px;
}

.typingIndicatorBubbleDot {
  width: 9px;
  height: 9px;
  margin-right: 4px;
  background-color: #000;
  border-radius: 50%;
  animation-name: bounce;
  animation-duration: 1.3s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
}

.typingIndicatorBubbleDot:first-of-type {
  margin: 0px 4px;
}
.typingIndicatorBubbleDot:nth-of-type(1) {
  animation-delay: 0.3s;
}

.typingIndicatorBubbleDot:nth-of-type(2) {
  animation-delay: 0.6s;
}

.typingIndicatorBubbleDot:nth-of-type(3) {
  animation-delay: 0.9s;
}

@keyframes bounce {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

.sidebar-container {
  padding: 40px 30px 15px 30px !important;
  border-right: 1px solid var(--Greyscale-100, rgba(12, 15, 19, 0.1));
  background: var(--Basic-Off-White, #f5f4f4);
  /* box-sizing: content-box !important; */
  height: calc(100vh - 24px) !important;
}

/* Custom tooltip inner styles */
.tooltip.bs-tooltip-right .tooltip-inner {
  background-color: #ffeb3b !important; /* Custom background color */
  color: #000 !important; /* Custom text color */
}

/* Custom tooltip arrow styles */
.tooltip.bs-tooltip-right .arrow::before {
  border-right-color: #ffeb3b !important; /* Custom arrow color */
}

.sidebar {
  font-size: 20px;
  width: 100%;
}
.sidebar li {
  width: 100%;
}
.sidebar li :hover {
  font-family: 'Montserrat';
  color: #fff;
}

.sidebar > .active {
  /* background-color: #000; */
  color: #fff !important;
  font-size: 20px;
  font-style: normal;
  font-weight: 600;
}

.bottom-profile-container {
  padding: 8px 16px;
}
/* @media screen and (max-width: 576px) {
  .bottom-profile-container {
    padding: 10px;
  }
} */

.sidebar > li > a {
  cursor: pointer !important;
}

.preview-profile {
  position: fixed;
  top: calc(100vh - 55px);
  width: fit-content !important;
}

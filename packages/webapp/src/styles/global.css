body {
  margin: 0px !important;
  padding: 0px !important;
  overflow-x: hidden !important;
  overflow-y: hidden !important;
}
.container {
  margin: 0px !important;
  padding: 0px !important;
}

.cursor-pointer {
  cursor: pointer;
}
button {
  max-height: 40px !important;
}

/* Page.css */
.loader {
  border: 8px solid #f3f3f3; /* Light grey */
  border-top: 8px solid #3498db; /* Blue */
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

.custom-z-index {
  z-index: 2;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.content-container {
  height: calc(100vh - 65px);
  overflow-y: hidden;
}
.sidebar-body {
  padding: 30px 30px 0px 30px !important;
  height: calc(100vh - 164px);
  overflow-y: scroll;
}
.sidebar-content {
  /* width: 100%; */
  /* position: relative; */
  /* height: '100%'; */
  /* overflow-y: auto; */
  /* margin: 0px 50px 0px 0px; */
  /* border-radius: 10px;
  border: 1px solid var(--Neutral-400, #989590); */
}

.nav-link {
  /* color: var(--Neutral-400, #fff); */
  transition: color 0.3s ease;
  font-family: 'Montserrat';
}
.onHover:hover {
  /* color: var(--Neutral-400, #fff); */
  font-weight: 600;
  font-family: 'Montserrat';
}
.nav-link:hover {
  /* color: var(--Neutral-400, #fff); */
  font-weight: 600;
  font-family: 'Montserrat';
}
.nav-link:active {
  /* color: var(--Neutral-400, #fff) !important; */
  font-weight: 600 !important;
  font-family: 'Montserrat' !important;
}
textarea {
  height: 40px;
}
.mr-12 {
  margin-right: 12px;
}

/* Header.css */
.header {
  z-index: 1 !important;
  position: sticky;
  border-bottom: 1px solid var(--Greyscale-100, rgba(12, 15, 19, 0.1));
  background: var(--Basic-Off-White, #f5f4f4);
  box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.14);
  padding: 12px 24px !important;
  /* padding-left: 50px !important;
  padding-right: 50px !important; */
  display: flex;
  margin: 0px;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  min-height: 64px;
}

.left-content {
}

.left-text {
  color: var(--Neutral-400, #989590);
  font-family: Montserrat;
  font-size: 20px;
  font-style: normal;
  font-weight: 700;
  line-height: normal;
}

.right-content {
  color: black;
}
.profile-container {
  position: relative;
  display: inline-block;
}

.profile-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  display: none;
  cursor: pointer;
}

.profile-container:hover .profile-dropdown {
  display: block;
  cursor: pointer;
}

/* .dropdown-menu {
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
} */

.username {
  cursor: pointer;
  color: var(--Neutral-400, #989590);
  font-size: 20px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

/**
 * Integration test script to verify communication fixes
 * Run this script to test the actual communication flow
 */

import logger from '../utils/logger.js';
import { connectSocket, joinRoom, emitMessage } from '../utils/socketUtils.js';

class CommunicationTester {
  constructor() {
    this.testResults = [];
    this.socket = null;
  }

  log(message, data = null) {
    console.log(`[TEST] ${message}`, data || '');
    logger.info(`[TEST] ${message}`, data);
  }

  logError(message, error = null) {
    console.error(`[TEST ERROR] ${message}`, error || '');
    logger.error(`[TEST ERROR] ${message}`, error);
  }

  addResult(testName, passed, message = '') {
    this.testResults.push({
      test: testName,
      passed,
      message,
      timestamp: new Date().toISOString(),
    });
  }

  async testSocketConnection() {
    this.log('Testing socket connection...');
    
    try {
      const apiUrl = 'https://api.dev.daiquiri.activate.bar';
      this.socket = connectSocket(apiUrl);
      
      if (this.socket) {
        this.addResult('Socket Connection', true, 'Socket created successfully');
        this.log('✓ Socket connection test passed');
        return true;
      } else {
        this.addResult('Socket Connection', false, 'Failed to create socket');
        this.logError('✗ Socket connection test failed');
        return false;
      }
    } catch (error) {
      this.addResult('Socket Connection', false, error.message);
      this.logError('✗ Socket connection test failed:', error);
      return false;
    }
  }

  async testRoomJoining() {
    this.log('Testing room joining...');
    
    try {
      const testRoomId = 'test-room-' + Date.now();
      joinRoom(testRoomId);
      
      this.addResult('Room Joining', true, `Joined room: ${testRoomId}`);
      this.log('✓ Room joining test passed');
      return true;
    } catch (error) {
      this.addResult('Room Joining', false, error.message);
      this.logError('✗ Room joining test failed:', error);
      return false;
    }
  }

  async testMessageEmission() {
    this.log('Testing message emission...');
    
    return new Promise((resolve) => {
      try {
        const testMessage = {
          data: {
            inputs: [
              {
                name: 'userInput',
                type: 'longText',
                data: 'Test message for communication verification',
                metaData: 'string',
              },
            ],
          },
          childId: 'test-child-' + Date.now(),
        };

        const callback = (acknowledgment) => {
          if (acknowledgment === 'Message received successfully') {
            this.addResult('Message Emission', true, 'Message acknowledged');
            this.log('✓ Message emission test passed');
            resolve(true);
          } else {
            this.addResult('Message Emission', false, `Unexpected acknowledgment: ${acknowledgment}`);
            this.logError('✗ Message emission test failed - unexpected acknowledgment');
            resolve(false);
          }
        };

        emitMessage('message', testMessage, 'test-room', null, callback);

        // Set a timeout in case no acknowledgment is received
        setTimeout(() => {
          this.addResult('Message Emission', false, 'No acknowledgment received within timeout');
          this.logError('✗ Message emission test failed - timeout');
          resolve(false);
        }, 10000);

      } catch (error) {
        this.addResult('Message Emission', false, error.message);
        this.logError('✗ Message emission test failed:', error);
        resolve(false);
      }
    });
  }

  async testConnectionRecovery() {
    this.log('Testing connection recovery...');
    
    try {
      // Simulate connection loss and recovery
      if (this.socket) {
        this.socket.disconnect();
        this.log('Simulated connection loss');
        
        // Wait a bit then reconnect
        setTimeout(() => {
          const newSocket = connectSocket('https://api.dev.daiquiri.activate.bar');
          if (newSocket) {
            this.addResult('Connection Recovery', true, 'Successfully recovered connection');
            this.log('✓ Connection recovery test passed');
          } else {
            this.addResult('Connection Recovery', false, 'Failed to recover connection');
            this.logError('✗ Connection recovery test failed');
          }
        }, 2000);
      }
      
      return true;
    } catch (error) {
      this.addResult('Connection Recovery', false, error.message);
      this.logError('✗ Connection recovery test failed:', error);
      return false;
    }
  }

  async testTimeoutHandling() {
    this.log('Testing timeout handling...');
    
    try {
      // This would test the timeout configuration
      const timeouts = {
        AGENT_RESPONSE_TIMEOUT: 120000,
        SOCKET_CONNECTION_TIMEOUT: 20000,
        HEARTBEAT_INTERVAL: 10000,
      };
      
      const hasValidTimeouts = Object.values(timeouts).every(timeout => 
        typeof timeout === 'number' && timeout > 0
      );
      
      if (hasValidTimeouts) {
        this.addResult('Timeout Configuration', true, 'All timeouts properly configured');
        this.log('✓ Timeout handling test passed');
        return true;
      } else {
        this.addResult('Timeout Configuration', false, 'Invalid timeout configuration');
        this.logError('✗ Timeout handling test failed');
        return false;
      }
    } catch (error) {
      this.addResult('Timeout Configuration', false, error.message);
      this.logError('✗ Timeout handling test failed:', error);
      return false;
    }
  }

  async runAllTests() {
    this.log('Starting communication integration tests...');
    
    const tests = [
      () => this.testSocketConnection(),
      () => this.testRoomJoining(),
      () => this.testMessageEmission(),
      () => this.testConnectionRecovery(),
      () => this.testTimeoutHandling(),
    ];

    for (const test of tests) {
      await test();
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    this.generateReport();
  }

  generateReport() {
    this.log('Generating test report...');
    
    const passed = this.testResults.filter(r => r.passed).length;
    const total = this.testResults.length;
    
    console.log('\n=== COMMUNICATION TEST REPORT ===');
    console.log(`Tests passed: ${passed}/${total}`);
    console.log(`Success rate: ${((passed / total) * 100).toFixed(1)}%`);
    console.log('\nDetailed Results:');
    
    this.testResults.forEach(result => {
      const status = result.passed ? '✓ PASS' : '✗ FAIL';
      console.log(`${status} - ${result.test}: ${result.message}`);
    });
    
    console.log('\n=== END REPORT ===\n');
    
    // Export logs for debugging
    const exportedLogs = logger.exportLogs();
    console.log('Exported logs for debugging:', exportedLogs.length, 'characters');
    
    return {
      passed,
      total,
      successRate: (passed / total) * 100,
      results: this.testResults,
      logs: exportedLogs,
    };
  }
}

// Export for use in other files
export default CommunicationTester;

// If running directly
if (typeof window !== 'undefined') {
  window.CommunicationTester = CommunicationTester;
  
  // Auto-run tests if requested
  if (window.location.search.includes('run-comm-tests=true')) {
    const tester = new CommunicationTester();
    tester.runAllTests();
  }
}

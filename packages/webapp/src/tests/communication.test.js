/**
 * Tests for Daiquiri-Spritz-Agent communication fixes
 */

import { jest } from '@jest/globals';

// Mock socket.io-client
const mockSocket = {
  connected: false,
  emit: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  disconnect: jest.fn(),
};

jest.mock('socket.io-client', () => {
  return jest.fn(() => mockSocket);
});

// Mock logger
jest.mock('../utils/logger', () => ({
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  debug: jest.fn(),
}));

import { connectSocket, joinRoom, emitMessage, disconnectSocket } from '../utils/socketUtils';
import logger from '../utils/logger';

describe('Communication Fixes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockSocket.connected = false;
  });

  describe('Socket Connection Management', () => {
    test('should prevent multiple simultaneous connections', () => {
      // First connection attempt
      connectSocket('http://localhost:3000');
      
      // Second connection attempt should reuse existing
      const socket2 = connectSocket('http://localhost:3000');
      
      expect(logger.info).toHaveBeenCalledWith('Socket connection already in progress');
    });

    test('should reuse existing healthy connection', () => {
      mockSocket.connected = true;
      
      const socket1 = connectSocket('http://localhost:3000');
      const socket2 = connectSocket('http://localhost:3000');
      
      expect(logger.info).toHaveBeenCalledWith('Using existing socket connection');
      expect(socket1).toBe(socket2);
    });

    test('should handle connection errors gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // Simulate connection error
      const error = new Error('Connection failed');
      jest.spyOn(require('socket.io-client'), 'default').mockImplementation(() => {
        throw error;
      });
      
      expect(() => connectSocket('http://localhost:3000')).toThrow('Connection failed');
      expect(logger.error).toHaveBeenCalledWith('Failed to create socket connection:', error);
      
      consoleSpy.mockRestore();
    });
  });

  describe('Room Management', () => {
    test('should store roomId for reconnection scenarios', () => {
      mockSocket.connected = true;
      connectSocket('http://localhost:3000');
      
      joinRoom('test-room-123');
      
      expect(logger.info).toHaveBeenCalledWith('Joining room: test-room-123');
      expect(mockSocket.emit).toHaveBeenCalledWith('joinRoom', 'test-room-123');
    });

    test('should handle missing roomId', () => {
      joinRoom(null);
      
      expect(logger.error).toHaveBeenCalledWith('Cannot join room: roomId is required');
      expect(mockSocket.emit).not.toHaveBeenCalled();
    });

    test('should queue room join when socket not connected', () => {
      mockSocket.connected = false;
      
      joinRoom('test-room-123');
      
      expect(logger.warn).toHaveBeenCalledWith('Socket not connected, will join room test-room-123 after connection');
    });
  });

  describe('Message Emission', () => {
    test('should emit message when socket is connected', async () => {
      mockSocket.connected = true;
      
      const callback = jest.fn();
      await emitMessage('test-event', { data: 'test' }, 'room-123', null, callback);
      
      expect(mockSocket.emit).toHaveBeenCalledWith('test-event', { data: 'test' }, callback);
    });

    test('should handle uninitialized socket', async () => {
      const initializeSocket = jest.fn();
      const callback = jest.fn();
      
      // Clear the socket reference
      disconnectSocket();
      
      await emitMessage('test-event', { data: 'test' }, 'room-123', initializeSocket, callback);
      
      expect(logger.error).toHaveBeenCalledWith('Socket not initialized', { 
        event: 'test-event', 
        roomId: 'room-123' 
      });
      expect(initializeSocket).toHaveBeenCalledWith('room-123', true);
    });
  });

  describe('Connection Recovery', () => {
    test('should attempt reconnection on disconnect', () => {
      const setError = jest.fn();
      const widgetCallBack = jest.fn();
      
      // This would be tested by simulating the setupHeartbeatAndReconnect function
      // For now, we'll test the basic error handling
      expect(setError).toBeDefined();
      expect(widgetCallBack).toBeDefined();
    });
  });

  describe('Page Reload Handling', () => {
    test('should store connection state in localStorage', () => {
      const mockLocalStorage = {
        setItem: jest.fn(),
        getItem: jest.fn(),
        removeItem: jest.fn(),
      };
      
      Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true,
      });
      
      // This would be tested in the chatbox component
      // For now, we verify localStorage methods are available
      expect(mockLocalStorage.setItem).toBeDefined();
      expect(mockLocalStorage.getItem).toBeDefined();
    });
  });
});

describe('Agent Response Timeout Handling', () => {
  test('should handle timeout responses', () => {
    // Mock a timeout response
    const timeoutResponse = {
      data: {
        statusCode: 408,
        message: 'Request timeout - Agent response took too long',
        result: null
      }
    };
    
    expect(timeoutResponse.data.statusCode).toBe(408);
    expect(timeoutResponse.data.message).toContain('timeout');
  });

  test('should use configurable timeouts', () => {
    const config = {
      timeouts: {
        AGENT_RESPONSE_TIMEOUT: 120000,
        SOCKET_CONNECTION_TIMEOUT: 20000,
      }
    };
    
    expect(config.timeouts.AGENT_RESPONSE_TIMEOUT).toBe(120000);
    expect(config.timeouts.SOCKET_CONNECTION_TIMEOUT).toBe(20000);
  });
});

describe('Error Handling and Logging', () => {
  test('should log errors with appropriate levels', () => {
    logger.error('Test error message', { context: 'test' });
    logger.warn('Test warning message');
    logger.info('Test info message');
    
    expect(logger.error).toHaveBeenCalledWith('Test error message', { context: 'test' });
    expect(logger.warn).toHaveBeenCalledWith('Test warning message');
    expect(logger.info).toHaveBeenCalledWith('Test info message');
  });
});

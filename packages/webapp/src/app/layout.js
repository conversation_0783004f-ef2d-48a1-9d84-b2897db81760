import React from 'react';
import PropTypes from 'prop-types';
import Header from '@/components/header/header';
import 'bootstrap/dist/css/bootstrap.min.css';
import '../styles/global.css';

export const metadata = {
  title: 'daiquiri',
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <link rel="icon" type="image/png" sizes="32x32" href="./favicon.png" />
      </head>
      <body>
        <div className="container-fluid px-0">
          <div className="row">
            <div className="col-12">
              {/* <ToastContainer /> */}
              {children}
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}

RootLayout.propTypes = {
  children: PropTypes.object,
};

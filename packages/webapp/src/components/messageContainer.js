import React, { useState } from 'react';
import DOMPurify from 'dompurify';
import PropTypes from 'prop-types';
import ClipboardIcon from '../../public/images/copy';
import '../styles/chatui.css';
import config from '../helper/config.json';

function Messages({
  message,
  customStyles,
  widgetCallBack,
  discardCallBack,
  discardButtonText,
  callbackButtonText,
}) {
  const [copyMsg, setCopyMsg] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const isWidget = config.envVariables.WIDGET === 'yes';

  // Define hover styles only if provided
  const hoverButtonStyle = customStyles?.messageHeader?.button?.hover || {};

  const handleCopy = (message) => {
    navigator.clipboard
      .writeText(message.text)
      .then(() => {
        setCopyMsg(true);
        setTimeout(() => {
          setCopyMsg(false);
        }, 2000);
      })
      .catch((err) => {
        console.error('Failed to copy text: ', err);
      });
  };

  const hasHtml = (str) => /<[^>]+>/i.test(str);
  const isHtmlContent = hasHtml(message.text || '');

  const renderContent = () => {
    if (isHtmlContent) {
      // Sanitize the incoming HTML but explicitly allow <iframe> so that
      // embedded content such as videos can be displayed inside the chat
      // bubble.  Only a limited set of attributes that are generally
      // required for safe video embeds are whitelisted to avoid opening up
      // security issues.

      const sanitizedHtml = DOMPurify.sanitize(message.text, {
        ADD_TAGS: ['iframe'],
        ADD_ATTR: [
          'allow',
          'allowfullscreen',
          'frameborder',
          'src',
          'width',
          'height',
          'loading',
          'referrerpolicy',
          'title',
        ],
      });

      return <span dangerouslySetInnerHTML={{ __html: sanitizedHtml }} />;
    }
    const lines = (message.text || '').split(/\n/);
    return lines.map((line, idx) => (
      <React.Fragment key={idx}>
        {line}
        {idx < lines.length - 1 && <br />}
      </React.Fragment>
    ));
  };

  return (
    <div
      key={message.text}
      className={`message-container ${message.type}`}
      // style={customStyles?.messageContainer}
    >
      <div
        className={`message ${message.type}`}
        style={{
          ...customStyles?.message?.[message.type],
          display: 'inline-block',
        }}
      >
        {renderContent()}
      </div>

      {message.type === 'received' && message.isCompleted && isWidget && (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
            marginTop: '10px',
          }}
        >
          <button
            className="sendButton"
            style={{
              ...customStyles?.discardButton,
              marginRight: '5px',
              display: 'block',
              flex: 1,
            }}
            onClick={() => discardCallBack(message.text)}
          >
            {discardButtonText ? discardButtonText : 'Discard'}
          </button>
          <button
            style={{
              ...customStyles?.callbackButton,
              marginLeft: '5px',
              display: 'block',
              flex: 1,
            }}
            className="sendButton"
            onClick={() => widgetCallBack(null, message.text)}
          >
            {callbackButtonText ? callbackButtonText : 'Use It'}
          </button>
        </div>
      )}
    </div>
  );
}

Messages.propTypes = {
  widgetCallBack: PropTypes.func.optional,
  message: PropTypes.object,
  customStyles: PropTypes.object,
  discardCallBack: PropTypes.func.isRequired,
  discardButtonText: PropTypes.string,
  callbackButtonText: PropTypes.string,
};
export default Messages;

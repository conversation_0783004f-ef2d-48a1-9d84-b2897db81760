import React from 'react';

const Tooltip = ({ hoveredAgent, tooltipStyle }) => (
  <div
    className="position-absolute p-2 bg-white border rounded shadow text-small custom-z-index"
    style={{
      top: tooltipStyle.top,
      left: tooltipStyle.left,
      maxWidth: '450px',
    }}
  >
    <h6 className="fw-semibold">{hoveredAgent.name}</h6>
    <p>{hoveredAgent.agentId.description}</p>
  </div>
);

export default Tooltip;

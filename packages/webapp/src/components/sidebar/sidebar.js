'use client';
// https://www.codeply.com/p/WGCqYEiPBg
import React, { useEffect, useState } from 'react';
import { isEmpty } from 'lodash';
import '@/styles/sidebar.css';
import Organization from '../../../public/images/organization.jsx';
import { getLocalStorageItem } from '@/utils/storage';
import Tooltip from '../tooltip';

const Menu = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [agents, setAgents] = useState([]);
  const [hoveredAgent, setHoveredAgent] = useState(null);
  const [tooltipStyle, setTooltipStyle] = useState({});

  useEffect(() => {
    const agent = getLocalStorageItem('agents');
    if (agent) {
      setAgents(agent);
    }
  }, []);
  const handleAgentHover = (agent, event) => {
    setHoveredAgent(agent);
    const tooltipPosition = {
      left: event.clientX + 25,
      top: event.clientY + -20,
    };
    setTooltipStyle(tooltipPosition);
  };

  const handleAgentMouseLeave = () => {
    setHoveredAgent(null);
  };

  return (
    <>
      {/* <button
        className="d-md-none fixed-top mt-5 ms-2 z-50 bg-secondary p-2 rounded-circle"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? 'Close' : 'Menu'}
      </button> */}

      <div
        className={`start-0 bottom-0 overflow-auto transition transform ${
          isOpen ? 'translate-x-0' : '-translate-x-100'
        } d-md-block`}
      >
        <div className="d-flex align-items-center mb-4">
          <Organization />
          <h5 className="ms-2">Organization</h5>
        </div>
        <hr />
        {hoveredAgent && (
          <Tooltip hoveredAgent={hoveredAgent} tooltipStyle={tooltipStyle} />
        )}
        {agents.length > 0 && (
          <>
            <ul className="list-unstyled">
              <h6 className="fw-semibold mb-2">Agents</h6>
              {agents.map((agent) => (
                <li
                  key={agent.agentId._id}
                  className="position-relative d-flex align-items-center text-small mb-2 pr-2 pt-2 pb-2 rounded cursor-pointer"
                >
                  <span
                    className="d-inline-block rounded-circle me-2"
                    style={{
                      backgroundColor: agent.color,
                      width: '1.1rem',
                      height: '1.1rem',
                    }}
                  ></span>
                  {agent.name}
                  <span
                    className="ms-auto"
                    onMouseEnter={(e) => handleAgentHover(agent, e)}
                    onMouseLeave={handleAgentMouseLeave}
                  >
                    <svg
                      width="20"
                      height="20"
                      viewBox="0 0 18 18"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="text-center cursor-pointer"
                    >
                      <desc>SVG Fallback Image</desc>
                      <path
                        fillRule="evenodd"
                        clipRule="evenodd"
                        d="M9.00008 2.12775C5.10577 2.12775 1.9488 5.28472 1.9488 9.17904C1.9488 13.0733 5.10577 16.2303 9.00008 16.2303C12.8944 16.2303 16.0514 13.0733 16.0514 9.17904C16.0514 5.28472 12.8944 2.12775 9.00008 2.12775ZM0.666748 9.17904C0.666748 4.57666 4.39771 0.845703 9.00008 0.845703C13.6025 0.845703 17.3334 4.57666 17.3334 9.17904C17.3334 13.7814 13.6025 17.5124 9.00008 17.5124C4.39771 17.5124 0.666748 13.7814 0.666748 9.17904ZM9.00008 8.53801C9.3541 8.53801 9.64111 8.82502 9.64111 9.17904V12.1705C9.64111 12.5245 9.3541 12.8115 9.00008 12.8115C8.64606 12.8115 8.35906 12.5245 8.35906 12.1705V9.17904C8.35906 8.82502 8.64606 8.53801 9.00008 8.53801ZM9.00008 5.76023C8.52803 5.76023 8.14538 6.1429 8.14538 6.61493C8.14538 7.08697 8.52803 7.46964 9.00008 7.46964H9.00863C9.48068 7.46964 9.86333 7.08697 9.86333 6.61493C9.86333 6.1429 9.48068 5.76023 9.00863 5.76023H9.00008Z"
                        fill="black"
                      />
                    </svg>
                  </span>
                </li>
              ))}
            </ul>
          </>
        )}
      </div>
    </>
  );
};

export default Menu;

'use client';
import React, { useState, useEffect, useRef } from 'react';
import { get, isArray } from 'lodash';
import { SpritzAPI } from 'api';
import { setLocalStorageItem } from '@/utils/storage';
import Sidebar from '@/components/sidebar/sidebar';
import ChatUI from '@/components/chatbox';
import '../styles/global.css';
import Header from './header/header';

function StartChat({ organizationId }) {
  const [loading, setLoading] = useState(true);
  const chatUIRef = useRef(null);

  useEffect(() => {
    getAgents();
  }, []);

  const getAgents = async () => {
    const urlParams = new URLSearchParams(window.location.search);
    const agentId = urlParams.get('agentID');
    let resp = await SpritzAPI.getAgents(organizationId);
    if (!isArray(resp)) {
      resp = [];
    }
    const agents = get(resp[0], 'agents', []);
    setLocalStorageItem('agents', agents);
    const selectedAgent = agents.find(
      (agent) => get(agent, 'agentId._id', '') === agentId
    );
    if (selectedAgent) {
      setLocalStorageItem('selectedAgent', selectedAgent);
    } else {
      setLocalStorageItem('selectedAgent', agents[0]);
    }
    setLoading(false);
  };

  const handleSelectAgentFromHeader = (agent) => {
    if (chatUIRef.current) {
      chatUIRef.current.handleSelectAgent(agent);
    }
  };

  return loading ? (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
      }}
    >
      <div className="loader"></div>
    </div>
  ) : (
    <div className="row mx-0">
      <div className="row mx-0 px-0">
        <Header onSelectAgent={handleSelectAgentFromHeader} />
      </div>
      <div className="col-12 col-xl-2 col-md-3 d-none d-lg-block sidebar-container">
        <Sidebar />
      </div>
      <div className="col-12 col-xl-10 col-lg-9 sidebar-content px-0">
        <ChatUI ref={chatUIRef} />
      </div>
    </div>
  );
}

export default StartChat;

import React from 'react';
import { createRoot } from 'react-dom/client';
import Chat<PERSON> from '../chatbox';
import errors from '../../helper/errorList.json';
import { SpritzAPI } from '../../api';
import { isArray, get } from 'lodash';
import LoadScripts from './loadScipts';

class Widget {
  constructor() {
    this.chatUIRef = React.createRef(); // Use createRef to store the ref for logging in Widget
  }
  init = (config) => {
    const {
      widgetCallBack,
      organizationId,
      agentId,
      styles,
      discardCallBack,
      discardButtonText,
      callbackButtonText,
    } = config;
    const container = document.querySelector('.widget-container');
    const getAgent = async () => {
      let resp = await SpritzAPI.getAgents(organizationId);
      try {
        if (!isArray(resp)) {
          resp = [];
        }
        const agents = get(resp[0], 'agents', []);
        const selectedAgent = agents.find(
          (agent) => get(agent, 'agentId._id', '') === agentId
        );
        return selectedAgent;
      } catch (e) {
        console.log('error in set agent =============', e);
      }
    };
    if (!container) {
      widgetCallBack(errors.widgetContainerNotFound, null);
      return;
    }

    if (!organizationId) {
      widgetCallBack(errors.organizationIdNotInitialized, null);
      return;
    }

    if (!agentId) {
      widgetCallBack(errors.agentIdNotInitialized, null);
      return;
    }

    const initializeWidget = async () => {
      const selectedAgent = await getAgent();
      const description = get(
        selectedAgent,
        'agentId.description',
        'Welcome! how can i assist you ?'
      );
      const root = createRoot(container);
      root.render(
        <ChatUI
          ref={this.chatUIRef}
          widgetCallBack={widgetCallBack}
          customStyle={styles}
          organizationId={organizationId}
          agentId={agentId}
          discardCallBack={discardCallBack}
          discardButtonText={discardButtonText}
          callbackButtonText={callbackButtonText}
          selectedAgentWidget={description}
        />
      );
    };

    const checkScriptsAndInitialize = () => {
      if (window.React && window.ReactDOM) {
        initializeWidget();
      } else {
        const scriptLoader = document.createElement('div');
        container.appendChild(scriptLoader);
        const root = createRoot(scriptLoader);
        root.render(<LoadScripts onLoad={initializeWidget} />);
      }
    };

    checkScriptsAndInitialize();
  };
  sendMessageExternal = async (message) => {
    // Wait until chatUIRef is ready
    await this.waitForChatUIRef();
    // Now send the message using the ref
    if (this.chatUIRef.current) {
      setTimeout(async () => {
        // Ensure state is updated before sending
        this.chatUIRef.current.setInputMessage(message);
      }, 1000);
      setTimeout(async () => {
        // Ensure state is updated before sending
        await this.chatUIRef.current.sendMessage();
        this.chatUIRef.current.setInputMessage('');
      }, 1200);
    } else {
      console.error('ChatUI instance is still not available.');
    }
  };

  // Helper function to wait for chatUIRef to be defined
  waitForChatUIRef = () => {
    return new Promise((resolve) => {
      const checkRef = () => {
        if (this.chatUIRef.current) {
          resolve();
        } else {
          setTimeout(checkRef, 100); // Check every 100ms
        }
      };
      checkRef();
    });
  };
}

if (typeof window !== 'undefined') {
  window.widget = new Widget();
}

export default Widget;

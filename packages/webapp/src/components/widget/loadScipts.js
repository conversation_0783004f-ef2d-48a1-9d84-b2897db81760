import React, { useEffect, useState } from 'react';

const LoadScripts = ({ onLoad }) => {
  const [scriptsLoaded, setScriptsLoaded] = useState(false);

  useEffect(() => {
    const loadScript = (src) => {
      return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.async = true;
        script.onload = resolve;
        script.onerror = reject;
        document.body.appendChild(script);
      });
    };

    const loadAllScripts = async () => {
      try {
        await loadScript('https://unpkg.com/react@18/umd/react.development.js');
        await loadScript(
          'https://unpkg.com/react-dom@18/umd/react-dom.development.js'
        );
        setScriptsLoaded(true);
        if (onLoad) onLoad();
      } catch (error) {
        console.error('Error loading scripts', error);
      }
    };

    loadAllScripts();
  }, [onLoad]);

  return null;
};

export default LoadScripts;

'use client';
import React from 'react';
import {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
  useCallback,
} from 'react';
import { get } from 'lodash';
import ChatUIComponent from '@/components/chatUIComponent';
import { ChatAPI } from '../api';
import '../styles/global.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import {
  connectSocket,
  joinRoom,
  emitMessage,
  onMessage,
  disconnectSocket,
  setupHeartbeatAndReconnect,
} from '../utils/socketUtils';

import '../styles/chatui.css';
import defaultCustomStyles from '@/helper/customStyle';

import { getLocalStorageItem, setLocalStorageItem } from '@/utils/storage';
import config from '../helper/config.json';
import errorList from '../helper/errorList.json';

const ChatUI = forwardRef(
  (
    {
      customStyle,
      widgetCallBack,
      organizationId,
      agentId,
      discardCallBack,
      discardButtonText,
      callbackButtonText,
      selectedAgentWidget,
    },
    ref
  ) => {
    const ORGANIZATION_ID = organizationId
      ? organizationId
      : config.envVariables.ORGANIZATION_ID;
    const selectAgent = getLocalStorageItem('selectedAgent');

    const [messages, setMessages] = useState([
      {
        text: get(selectAgent, 'agentId.description', selectedAgentWidget),
        type: 'received',
        id: '',
      },
    ]);
    const [inputMessage, setInputMessage] = useState('');
    const [isTyping, setIsTyping] = useState(false);
    const [error, setError] = useState(null);
    const [messageHistory, setMessageHistory] = useState([]);
    const [agents, setAgents] = useState([]);
    const [isInputDisabled, setIsInputDisabled] = useState(false);
    const [isButtonDisabled, setIsButtonDisabled] = useState(true);
    const [selectedAgent, setSelectedAgent] = useState({});
    const [childId, setChildId] = useState('');
    const [roomId, setRoomId] = useState('');
    const customStyles = customStyle ? customStyle : defaultCustomStyles;

    const handleMessage = (message) => {
      setChildId(message._id);
      const messageData =
        message.status === 'awaitingInput'
          ? message.inputs
          : message.successResp.outputs;
      const updatedDataArray = messageData.map(
        ({ _id, validation, label, ...rest }) => rest
      );
      const updatedData = updatedDataArray.map((item) => ({
        ...item,
        metaData: 'string',
      }));
      setMessageHistory(updatedData);
      setIsTyping(false);

      let data = {};
      if (updatedData.length > 2) {
        data = { text: updatedData[1].data, type: 'received' };
        setIsInputDisabled(false);
        setIsButtonDisabled(false);
      } else if (updatedData.length === 2) {
        data = {
          text: updatedData[1].data,
          type: 'received',
          isCompleted: true,
        };
        setIsInputDisabled(true);
        setIsButtonDisabled(true);
      }

      setMessages((prevMessages) => [...prevMessages, data]);
      // smoothScrollToBottom(document.querySelector('.chat-body'));
      setError(null); // Clear any previous errors
    };

    const getRoomId = useCallback(
      (currentRoomId) => {
        if (config.envVariables.WIDGET === 'no') {
          const urlParams = new URLSearchParams(window.location.search);
          return urlParams.get('id');
        }
        return currentRoomId;
      },
      [] // No dependencies, as it only relies on constants
    );

    // Memoized socket initialization function
    const initializeSocket = useCallback(
      (id, disconnected = false) => {
        const socket = connectSocket(config.envVariables.API_BASE_URL);

        const { handleDisconnect, handleHeartbeatResponse, heartbeatInterval } =
          setupHeartbeatAndReconnect(setError, widgetCallBack);

        const handleError = (errorMessage) => {
          setError(errorMessage);
          if (errorMessage === 'Failed to send message') {
            resendMessage();
          }
        };

        // Attach socket listeners
        onMessage('message', handleMessage);
        onMessage('error', handleError);
        onMessage('heartbeat', handleHeartbeatResponse);

        // Join room and get messages
        joinRoom(id);
        if (!disconnected) {
          getHistoryMessages(id);
        }

        // Return cleanup function
        return () => {
          clearInterval(heartbeatInterval);
          socket.off('disconnect', handleDisconnect);
          socket.off('heartbeat', handleHeartbeatResponse);
          disconnectSocket();
        };
      },
      [setError, widgetCallBack] // Dependencies to prevent unnecessary re-creation
    );

    useEffect(() => {
      const id = getRoomId(roomId); // Get room ID
      setRoomId(id);

      if (id) {
        const cleanup = initializeSocket(id); // Set up socket and listeners
        return cleanup; // Cleanup on unmount or dependency change
      }
    }, [roomId]);

    useEffect(() => {
      const agent = getLocalStorageItem('agents');
      const selectedAgent = getLocalStorageItem('selectedAgent');
      if (selectedAgent) {
        setSelectedAgent(selectedAgent);
      }
      if (agent) {
        setAgents(agent);
      }
    }, []);

    const getHistoryMessages = async (id) => {
      const resp = await ChatAPI.getChat(ORGANIZATION_ID, id);
      const messageResp = get(resp, 'data', []);
      const filteredData = messageResp.filter((item) =>
        item.hasOwnProperty('text')
      );
      setChildId(filteredData[filteredData.length - 1].id);
      setMessages([messages[0], ...filteredData]);
    };

    const handleSelectAgent = (agent) => {
      setLocalStorageItem('selectedAgent', agent);
      setSelectedAgent(agent);

      if (typeof window !== 'undefined') {
        const { pathname } = window.location;
        const url = new URL(window.location);

        // Set the new parameter
        url.searchParams.set('id', agent.agentId._id);

        // Update the URL without reloading the page
        window.history.replaceState(window.history.state, '', url);

        // Use pushState to navigate to the new URL without reloading
        if (pathname && pathname !== '') {
          setMessages([
            {
              text: get(
                agent,
                'agentId.description',
                'Welcome! how can i assist you ?'
              ),
              type: 'received',
              id: '',
            },
          ]);
          setIsTyping(false);
          setIsInputDisabled(false);
          setIsButtonDisabled(false);
          window.history.pushState(
            {},
            '',
            `${pathname}?agentID=${agent.agentId._id}`
          );
        } else {
          setMessages([
            {
              text: get(
                agent,
                'agentId.description',
                'Welcome! how can i assist you ?'
              ),
              type: 'received',
              id: '',
            },
          ]);
          setIsTyping(false);
          setIsInputDisabled(false);
          setIsButtonDisabled(false);
          window.history.pushState({}, '', `/?agentID=${agent.agentId._id}`);
        }
      }
    };

    if (config.envVariables.WIDGET === 'no') {
      useImperativeHandle(ref, () => ({
        handleSelectAgent,
      }));
    }
    useImperativeHandle(ref, () => ({
      sendMessage,
      setInputMessage,
    }));
    const fetchData = async (inputMessage = 'Write my Profile for chef') => {
      setMessages([...messages, { text: inputMessage, type: 'sent' }]);
      let selectedAgentId = '';
      setIsTyping(true);
      setIsInputDisabled(true);
      setIsButtonDisabled(true);
      const isWidget = config.envVariables.WIDGET;
      const selectedAgent = getLocalStorageItem('selectedAgent');
      if (isWidget === 'yes' || get(selectedAgent, 'agentId._id', '') === '') {
        selectedAgentId = agentId ? agentId : config.envVariables.AGENT_ID;
      } else {
        selectedAgentId = get(selectedAgent, 'agentId._id', '');
      }
      const data = {
        inputs: [
          {
            name: 'userInput',
            type: 'longText',
            data: inputMessage,
            metaData: 'string',
          },
        ],
        agentId: selectedAgentId,
        organizationId: organizationId
          ? organizationId
          : config.envVariables.ORGANIZATION_ID,
        metaData: {
          name: 'sachin',
        },
        callbackUrl: config.envVariables.CALL_BACK_URL,
      };
      let id = '';
      if (config.envVariables.WIDGET === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        id = urlParams.get('id');
        setRoomId(id);
      } else {
        id = roomId;
      }
      try {
        if (!id) {
          const response = await ChatAPI.startChat(data);
          if (response.statusCode === 200) {
            const id = response.data._id;
            setRoomId(id);
            if (
              typeof window !== 'undefined' &&
              config.envVariables.WIDGET === 'no'
            ) {
              const url = new URL(window.location);
              url.searchParams.set('id', id);
              window.history.replaceState(window.history.state, '', url);
              // window.location.href = `/${response.data._id}`;
            }
            connectSocket(config.envVariables.API_BASE_URL);
            joinRoom(id);
          } else {
            errorList(errorList.serverNotResponding, null);
          }
        }
      } catch (error) {
        // setError(error);
      }
    };

    const sendMessage = async (msg) => {
      let id = '';
      if (config.envVariables.WIDGET === 'no') {
        const urlParams = new URLSearchParams(window.location.search);
        id = urlParams.get('id');
        setRoomId(id);
      } else {
        id = roomId;
      }
      if (inputMessage.trim() !== '') {
        if (!id) {
          await fetchData(inputMessage);
        } else {
          const index = messageHistory.findIndex(
            (item) => item.name === 'userInput'
          );
          if (index !== -1) {
            messageHistory[index].data = inputMessage;
          }

          const data = {
            inputs: messageHistory,
          };
          emitMessage(
            'message',
            { data, childId },
            roomId,
            initializeSocket,
            (acknowledgment) => {
              if (acknowledgment === 'Message received successfully') {
                // Need to handle it properly
              }
            }
          );
          // smoothScrollToBottom(document.querySelector('.chat-body'));
          setIsTyping(true);
          setIsInputDisabled(true);
          setIsButtonDisabled(true);
          setError(null);
          setMessages([
            ...messages,
            { text: inputMessage, type: 'sent', id: '' },
          ]);
          setInputMessage('');
        }
      }
    };

    const resendMessage = () => {
      const lastMessage = messages[messages.length - 1];
      if (lastMessage || error) {
        emitMessage(
          'message',
          { text: lastMessage.text, roomId },
          (acknowledgment) => {
            if (acknowledgment === 'Message received successfully') {
              setIsTyping(false);
              setIsInputDisabled(false);
              setIsButtonDisabled(false);
              setError(null);
            }
          }
        );
      }
    };

    const handleInputChange = (event) => {
      setInputMessage(event.target.value);
    };

    function smoothScrollToBottom(element) {
      const scrollHeight = element.scrollHeight;
      const clientHeight = element.clientHeight;
      const maxScrollTop = scrollHeight - clientHeight;
      const currentScrollTop = element.scrollTop;
      const targetScrollTop = Math.min(
        maxScrollTop,
        currentScrollTop + clientHeight
      );
      const distance = targetScrollTop - currentScrollTop;
      const duration = 300; // Adjust the duration as needed

      let start = null;
      function step(timestamp) {
        if (!start) start = timestamp;
        const progress = timestamp - start;
        const increment = (distance * progress) / duration;
        element.scrollTo(0, currentScrollTop + increment);
        if (progress < duration) {
          requestAnimationFrame(step);
        }
      }
      requestAnimationFrame(step);
    }

    return (
      <ChatUIComponent
        agents={agents}
        messages={messages}
        handleInputChange={handleInputChange}
        sendMessage={sendMessage}
        customStyles={customStyles}
        smoothScrollToBottom={smoothScrollToBottom}
        inputMessage={inputMessage}
        setInputMessage={setInputMessage}
        isTyping={isTyping}
        isInputDisabled={isInputDisabled}
        selectedAgent={selectedAgent}
        handleSelectAgent={handleSelectAgent}
        widgetCallBack={widgetCallBack}
        discardCallBack={discardCallBack}
        discardButtonText={discardButtonText}
        callbackButtonText={callbackButtonText}
        isButtonDisabled={isButtonDisabled}
      />
    );
  }
);

export default ChatUI;

import React, { useRef, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import '../styles/chatui.css';
import { Dropdown } from 'react-bootstrap';
import { get } from 'lodash';
import Messages from './messageContainer';
import config from '../helper/config.json';

const ChatUIComponent = ({
  messages,
  handleInputChange,
  sendMessage,
  customStyles,
  smoothScrollToBottom,
  isTyping,
  isInputDisabled,
  inputMessage,
  setInputMessage,
  agents = [],
  handleSelectAgent,
  selectedAgent,
  widgetCallBack,
  discardCallBack,
  discardButtonText,
  callbackButtonText,
}) => {
  const chatBodyRef = useRef(null);
  const textareaRef = useRef(null);
  const [inputDisabled, setInputDisable] = useState(false);

  useEffect(() => {
    smoothScrollToBottom(chatBodyRef.current);
  }, [messages]);

  // Focus textarea if input is not disabled
  useEffect(() => {
    if (!isInputDisabled) {
      textareaRef.current.focus();
    }
  }, [isInputDisabled, messages]);

  // Disable input if any message is completed
  useEffect(() => {
    const hasCompletedMessage = messages.some((message) => message.isCompleted);
    if (hasCompletedMessage) {
      setInputDisable(true);
    }
  }, [messages]);

  const handleSendMessage = () => {
    const textarea = textareaRef.current;
    sendMessage();
    setInputMessage('');
    textarea.style.height = 'auto'; // Reset height
  };

  const handleTextareaChange = (event) => {
    setInputMessage(event.target.value);
    handleInputChange(event);
    adjustTextareaHeight();
  };

  const adjustTextareaHeight = () => {
    const textarea = textareaRef.current;
    textarea.style.height = 'auto'; // Reset the height to auto
    textarea.style.height = `${Math.min(textarea.scrollHeight, 150)}px`; // Adjust height dynamically
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div
      className="chat-container"
      style={config.envVariables.WIDGET === 'yes' ? { height: '100%' } : {}}
    >
      <div className="chat-body" ref={chatBodyRef} style={customStyles?.body}>
        {messages.map((message, index) => (
          <Messages
            index={index}
            message={message}
            key={index}
            customStyles={customStyles}
            widgetCallBack={widgetCallBack}
            discardCallBack={discardCallBack}
            discardButtonText={discardButtonText}
            callbackButtonText={callbackButtonText}
          />
        ))}
        {isTyping && (
          <div className="typing-indicator-container">
            <div
              className="typing-indicator"
              style={customStyles?.typingIndicator}
            >
              <div className="typingIndicatorBubble">
                <div className="typingIndicatorBubbleDot"></div>
                <div className="typingIndicatorBubbleDot"></div>
                <div className="typingIndicatorBubbleDot"></div>
              </div>
            </div>
          </div>
        )}
      </div>
      <div className="chat-footer" style={customStyles?.footer}>
        {agents.length > 0 && (
          <Dropdown className="dropdown-up d-none d-lg-block mr-12">
            <Dropdown.Toggle
              className="custom-dropdown-toggle"
              id="dropdown-basic"
            >
              <span
                className="d-inline-block rounded-circle me-2 dropdown-icon"
                style={{
                  backgroundColor: get(selectedAgent, 'color', 'orange'),
                  width: '1rem',
                  height: '1rem',
                  marginBottom: '-1px',
                  marginRight: '25px',
                }}
              ></span>
              <span className="agent-name">
                {get(selectedAgent, 'name', 'Interviewer')}
              </span>
            </Dropdown.Toggle>
            <Dropdown.Menu>
              {agents.map((agent) => (
                <Dropdown.Item
                  key={agent.name} // Make sure each item has a unique key
                  onClick={() => handleSelectAgent(agent)}
                >
                  {agent.name}
                </Dropdown.Item>
              ))}
            </Dropdown.Menu>
          </Dropdown>
        )}

        <textarea
          ref={textareaRef}
          value={inputMessage}
          id="message-input"
          onKeyDown={handleKeyPress}
          placeholder="Write your message here"
          onChange={handleTextareaChange}
          style={customStyles?.textarea}
          disabled={isInputDisabled || inputDisabled}
          rows="1"
        />
        <button
          type="button"
          className="sendButton"
          onClick={() => handleSendMessage()}
          style={
            inputMessage === '' || inputDisabled
              ? customStyles?.button?.disabled
              : customStyles?.button
          }
          disabled={inputMessage === '' || inputDisabled}
        >
          Send
        </button>
      </div>
    </div>
  );
};

ChatUIComponent.propTypes = {
  isTyping: PropTypes.bool.isRequired,
  isInputDisabled: PropTypes.bool.isRequired,
  messages: PropTypes.array.isRequired,
  handleInputChange: PropTypes.func.isRequired,
  sendMessage: PropTypes.func.isRequired,
  customStyles: PropTypes.object,
  smoothScrollToBottom: PropTypes.func.isRequired,
  inputMessage: PropTypes.string.isRequired,
  setInputMessage: PropTypes.func.isRequired,
  agents: PropTypes.array.isRequired,
  handleSelectAgent: PropTypes.func.isRequired,
  selectedAgent: PropTypes.object,
  widgetCallBack: PropTypes.func.isRequired,
  discardCallBack: PropTypes.func.isRequired,
  discardButtonText: PropTypes.string,
  callbackButtonText: PropTypes.string,
  isButtonDisabled: PropTypes.bool,
};

export default ChatUIComponent;

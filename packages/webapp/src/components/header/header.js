'use client';
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Dropdown } from 'react-bootstrap';
import { get } from 'lodash';
import { getLocalStorageItem, setLocalStorageItem } from '@/utils/storage';
import '../../styles/header.css';
import Logo from '../../../public/images/logo/Logo.svg';

const Header = ({ onSelectAgent }) => {
  const [agents, setAgents] = useState([]);
  const [selectedAgent, setSelectedAgent] = useState({});
  useEffect(() => {
    const agent = getLocalStorageItem('agents');
    const selectedAgent = getLocalStorageItem('selectedAgent');
    if (selectedAgent) {
      setSelectedAgent(selectedAgent);
    }
    if (agent) {
      setAgents(agent);
    }
  }, []);

  const handleButtonClick = (agent) => {
    setSelectedAgent(agent);
    onSelectAgent(agent);
  };

  return (
    <div className="header">
      <div className="left-content">
        <img src={Logo.src} />
      </div>
      <div className="right-content  d-block d-lg-none">
        {agents.length > 0 && (
          <Dropdown className="dropdown-down" drop={'down'}>
            <Dropdown.Toggle
              className="custom-dropdown-toggle"
              id="dropdown-basic"
            >
              <span
                className="d-inline-block rounded-circle me-2 dropdown-icon"
                style={{
                  backgroundColor: 'orange',
                  width: '1rem',
                  height: '1rem',
                  marginBottom: '-1px',
                  marginRight: '25px',
                }}
              ></span>
              <span className="agent-name">
                {get(selectedAgent, 'name', 'Interviewer')}
              </span>
            </Dropdown.Toggle>
            <Dropdown.Menu>
              {agents.map((agent) => (
                <Dropdown.Item
                  key={agent.name} // Make sure each item has a unique key
                  onClick={() => handleButtonClick(agent)}
                >
                  {agent.name}
                </Dropdown.Item>
              ))}
            </Dropdown.Menu>
          </Dropdown>
        )}
      </div>
    </div>
  );
};

Header.propTypes = {
  rightComponent: PropTypes.element,
};

export default Header;

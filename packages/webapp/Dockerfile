FROM node:20-alpine as build

RUN apk update && apk upgrade

WORKDIR /app

ARG ENV

COPY . .
RUN npm ci
RUN cp .env.${ENV} .env.local && npm run build
RUN npm prune --production

FROM node:20-alpine

# Install chamber
USER root
ENV CHAMBER_VERSION=2.10.6
ENV CHAMBER_SHA256SUM=8b2750d2f93c6014c7a26d5695472a9d164624915fb140879abe77d746356f5f
RUN apk add curl
RUN curl -Ls https://github.com/segmentio/chamber/releases/download/v${CHAMBER_VERSION}/chamber-v${CHAMBER_VERSION}-linux-amd64 > chamber-linux-amd64 && \
    echo "${CHAMBER_SHA256SUM}  chamber-linux-amd64" > chamber_SHA256SUMS && \
    sha256sum -c chamber_SHA256SUMS && \
    rm chamber_SHA256SUMS && \
    chmod +x chamber-linux-amd64 && \
    mv chamber-linux-amd64 /usr/local/bin/chamber

# Build args
ARG ENV

WORKDIR /app

ENV PATH /app/node_modules/.bin:$PATH

COPY --chown=node:node --from=build app/node_modules /app/node_modules
COPY --chown=node:node --from=build app/public /app/public
COPY --chown=node:node --from=build app/.next /app/.next
COPY --chown=node:node --from=build app/next.config.mjs /next.config.mjs
COPY --chown=node:node --from=build app/package.json /app/package.json


RUN echo -e "#!/bin/ash\nchamber exec ${ENV}/daiquiri/webapp -- npm start" > ./entrypoint.sh

RUN chmod +x ./entrypoint.sh

USER node
ENV NODE_ENV production

ENTRYPOINT ["./entrypoint.sh"]


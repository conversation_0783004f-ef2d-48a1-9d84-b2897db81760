<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Widget Integration</title>
  </head>
  <body>
    <div id="widget-container"></div>

    <!-- Load React and ReactDOM -->
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>

    <!-- Load your widget bundle -->
    <script src="http://127.0.0.1:8081/page.bundle.js"></script>

    <script>
      // Ensure React and ReactDOM are loaded before initializing the widget
      document.addEventListener('DOMContentLoaded', function () {
        if (typeof React !== 'undefined' && typeof ReactDOM !== 'undefined') {
          console.log(widgetContainer);
          console.log(MyWidget);
          const widgetContainer = document.getElementById('widget-container');
          if (widgetContainer && typeof MyWidget !== 'undefined') {
            // Example initialization of MyWidget
            ReactDOM.render(
              React.createElement(MyWidget.default),
              widgetContainer
            );
          } else {
            console.error('Widget container or MyWidget is not defined.');
          }
        } else {
          console.error('React or ReactDOM is not loaded correctly.');
        }
      });
    </script>
  </body>
</html>

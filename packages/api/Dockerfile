FROM node:20-alpine

# Install chamber
USER root
ENV CHAMBER_VERSION=2.10.6
ENV CHAMBER_SHA256SUM=8b2750d2f93c6014c7a26d5695472a9d164624915fb140879abe77d746356f5f
RUN apk add --no-cache curl && \
    curl -Ls https://github.com/segmentio/chamber/releases/download/v${CHAMBER_VERSION}/chamber-v${CHAMBER_VERSION}-linux-amd64 > chamber-linux-amd64 && \
    echo "${CHAMBER_SHA256SUM}  chamber-linux-amd64" > chamber_SHA256SUMS && \
    sha256sum -c chamber_SHA256SUMS && \
    rm chamber_SHA256SUMS && \
    chmod +x chamber-linux-amd64 && \
    mv chamber-linux-amd64 /usr/local/bin/chamber

# Build args
ARG ENV

WORKDIR /app

ENV PATH /app/node_modules/.bin:$PATH

COPY package.json /app/
RUN npm install --production

COPY . /app/

RUN echo -e "#!/bin/sh\nchamber exec ${ENV}/daiquiri/api -- node index.js" > ./entrypoint.sh && \
    chmod +x ./entrypoint.sh

ENV NODE_ENV production

USER node

ENTRYPOINT ["./entrypoint.sh"]


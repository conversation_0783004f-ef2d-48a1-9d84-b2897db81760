const Joi = require('joi');

exports.Chat = {
  chat: {
    payload: Joi.object({
      inputs: Joi.array().items(
        Joi.object({
          name: Joi.string(),
          type: Joi.string(),
          data: Joi.string(),
          metaData: Joi.string(),
        })
      ),
      agentId: Joi.string(),
      organizationId: Joi.string(),
      metaData: Joi.object(),
      callbackUrl: Joi.string(),
    }),
  },
  params: {
    params: Joi.object({
      rootId: Joi.string().required(),
      organizationId: Joi.string().required(),
    }),
  },
};

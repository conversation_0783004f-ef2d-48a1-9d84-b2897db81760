'use strict';
const Pack = require('../../../package.json');

const hapiSwagger = {
  plugin: require('hapi-swagger'),
  options: {
    schemes: ['http', 'https'],
    // host: process.env.API_BASE_URL,
    grouping: 'tags',
    expanded: 'none',
    tags: [],
    info: {
      title: 'Daiquiri Documentation',
      version: Pack.version,
    },
    securityDefinitions: {
      AUTH0_TOKEN: {
        description: 'Auth0 jwt token use for api authentication',
        type: 'apiKey',
        name: 'Authorization',
        in: 'header',
      },
    },
  },
};

module.exports = hapiSwagger;

const logging = require('./logging');
const queryBuilder = require('./queryBuilder');
const version = require('./version');
const swagger = require('./swagger');
let plugins = [
  logging,
  queryBuilder,
  version,
  {
    plugin: require('hapi-auth-jwt2'),
  },
  {
    plugin: require('@hapi/inert'),
  },
  {
    plugin: require('@hapi/vision'),
  },
  {
    plugin: require('@hapi/inert'),
  },
  swagger,
];

/**
 * Register all routes in plugins
 * Simply add new routes in routes/index.js file for routing.
 */
const routes = require('../../routes/index');
plugins = plugins.concat(routes);

module.exports = plugins;

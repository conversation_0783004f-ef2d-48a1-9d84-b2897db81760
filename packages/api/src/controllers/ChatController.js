const _ = require('lodash');
const ErrorHandler = require('../lib/utils/ErrorHandler');
const { wrapper } = require('../lib/utils/wrapper');
const { emitAgentStatus } = require('../lib/utils/socketHandler');
const { get } = require('lodash');

/**
 * @class Chat Controller
 */
class ChatController {
  /**
   * @param {*} request
   * @param {*} h
   * @returns message and status code
   */
  static async chat(request, h) {
    try {
      global.logger.info('ChatController:chat - method called');
      const payload = request.payload;
      const data = {
        url: `${process.env.CHAT_AGENT_URL}/v2/job`,
        method: 'POST',
        data: payload,
        headers: {
          Authorization: `${process.env.CHAT_AGENT_TOKEN}`,
          'Content-Type': 'application/json',
          Origin: process.env.APP_BASE_URL,
        },
      };
      global.logger.info('ChatController:chat - call agent for chat');
      const result = await wrapper(data);
      if (result.data.statusCode !== 200) {
        global.logger.info('ChatController:chat - call agent getting error');
        return h
          .response({
            statusCode: result.data.statusCode,
            message: result.data.message,
            data: result.data.result,
          })
          .code(result.data.statusCode);
      } else {
        global.logger.info('ChatController:chat - called successfully');
        return h
          .response({
            statusCode: result.data.statusCode,
            message: result.data.message,
            data: result.data.result,
          })
          .code(result.data.statusCode);
      }
    } catch (error) {
      global.logger.info(`ChatController:chat - error ${error}`);

      return ErrorHandler.error(error, { msg: error, code: 400 });
    }
  }
  static async getChat(request, h) {
    try {
      global.logger.info('ChatController:getChat - method called');
      const { rootId, organizationId } = request.params;
      const agentUrl = process.env.CHAT_AGENT_URL;
      const data = {
        url: `${agentUrl}/v2/job`,
        method: 'GET',
        headers: {
          Authorization: `${process.env.CHAT_AGENT_TOKEN}`,
          'Content-Type': 'application/json',
          Origin: process.env.APP_BASE_URL,
        },
        params: {
          $or: `_id|${rootId},rootJobId|${rootId}`,
          organizationId,
        },
      };
      const result = await wrapper(data);
      let outputs = [];
      const outputData = get(result, 'data.data.docs');
      try {
        outputData.forEach((item) => {
          // Add 'sent' messages
          get(item, 'inputs').forEach((input) => {
            if (input.name === 'userInput' && input.data) {
              outputs.push({ type: 'sent', text: input.data, id: item._id });
            }
          });

          // Add 'received' messages
          let isCompleted = false;
          let data;
          get(item, 'successResp.outputs').forEach((output) => {
            if (output.name === 'output' && output.data) {
              data = output.data;
            }
            if (output.name === 'isComplete' && output.data === true) {
              isCompleted = true;
            }
          });
          if (isCompleted) {
            outputs.push({
              type: 'received',
              text: data,
              id: item._id,
              isCompleted,
            });
          } else {
            outputs.push({
              type: 'received',
              text: data,
              id: item._id,
            });
          }
        });
      } catch (err) {
        console.log(err, '============err');
      }
      global.logger.info('ChatController:getChat - called successfully');
      return h
        .response({
          statusCode: result.data.statusCode,
          message: result.data.message,
          data: outputs,
        })
        .code(result.data.statusCode);
    } catch (error) {
      global.logger.info(`ChatController:getChat - error ${error}`);

      return ErrorHandler.error(error, { msg: error, code: 400 });
    }
  }

  static async agentResponse(request, h) {
    try {
      global.logger.info('ChatController:agentResponse - method called');

      const payload = request.payload;
      if (payload.status === 'awaitingInput') {
        global.logger.info(
          `ChatController:agentResponse - method emit data roomId - ${payload._id}`
        );
        emitAgentStatus(get(payload, 'rootJobId', payload._id), payload);
      }
      if (
        get(payload, 'successResp.outputs', 0).length === 2 &&
        payload.status === 'completed'
      ) {
        emitAgentStatus(payload.rootJobId, payload);
      }
      return h
        .response({
          statusCode: 200,
          message: '',
          data: {},
        })
        .code(200);
    } catch (error) {
      global.logger.info(`ChatController:agentResponse - error ${error}`);
      return ErrorHandler.error(error, { msg: error, code: 400 });
    }
  }
}

module.exports = ChatController;

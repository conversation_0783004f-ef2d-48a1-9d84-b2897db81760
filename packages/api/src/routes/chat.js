const ChatController = require('../controllers/ChatController');
const { Chat } = require('../validations/chat');
module.exports = {
  plugin: {
    async register(server) {
      server.route([
        {
          method: 'POST',
          path: '/',
          options: {
            auth: false,
            tags: ['api', 'Chat'],
            pre: [],
            validate: Chat.chat,
            handler: ChatController.chat,
            description: 'Chat with agent',
          },
        },
        {
          method: 'GET',
          path: '/{rootId}/{organizationId}',
          options: {
            auth: false,
            tags: ['api', 'Chat'],
            pre: [],
            validate: Chat.params,
            handler: ChatController.getChat,
            description: 'Get chat from spritz',
          },
        },
        {
          method: 'POST',
          path: '/agent',
          options: {
            auth: false,
            tags: ['api', 'Chat'],
            pre: [],
            validate: {},
            handler: ChatController.agentResponse,
            description: 'Get chat from spritz',
          },
        },
      ]);
    },
    version: process.env.API_VERSION,
    name: 'chat',
  },
};

/* eslint-disable comma-dangle */
'use strict';

const { wrapper } = require('../utils/wrapper');
const Socket = (() => {
  let io;
  return {
    getInstance: () => {
      return io;
    },
    createInstance: (listener) => {
      try {
        // init socket instance
        io = require('socket.io')(listener, {
          cors: {
            origin: '*',
          },
        });

        // on connect
        io.on('connection', (socket) => {
          global.socketobj = socket;

          socket.on('disconnect', () => {
            // Handle socket disconnection
          });

          // Join a specific room
          socket.on('joinRoom', (roomId) => {
            socket.join(roomId);
          });

          // Handle incoming messages
          socket.on('message', async (data, acknowledge) => {
            const payload = {
              url: `${process.env.CHAT_AGENT_URL}/v2/job/awaiting/${data.childId}`,
              data: data.data,
              method: 'PATCH',
              headers: {
                Authorization: `${process.env.CHAT_AGENT_TOKEN}`,
                'Content-Type': 'application/json',
                Origin: process.env.APP_BASE_URL,
              },
            };
            const result = await wrapper(payload);
            // Broadcast the message to the specific room
            if (result.data.statusCode === 200) {
              // io.to(roomID).emit('message', {
              //   type: 'received',
              //   data: result.data.data.inputs,
              // });
              acknowledge('Message received successfully');
            }
            // Send acknowledgment
          });

          let isAlive = true;
          socket.on('heartbeat', () => {
            isAlive = true;
          });

          const heartbeatInterval = setInterval(() => {
            if (!isAlive) {
              // Connection lost, handle it
              clearInterval(heartbeatInterval);
              socket.disconnect(true);
            }
            isAlive = false;
            socket.emit('heartbeat');
          }, 10000);

          socket.on('disconnect', () => {
            clearInterval(heartbeatInterval);
          });
        });

        return io;
      } catch (error) {
        // Handle error
        console.error(`Socket encountered an error: ${error}`);
      }
    },
  };
})();

module.exports = Socket;

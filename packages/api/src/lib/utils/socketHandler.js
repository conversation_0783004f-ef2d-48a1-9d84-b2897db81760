const Socket = require('../services/socket');

/**
 * Handle agent-status socket event
 *
 * @param {string} room
 * @param {*} data
 */
const emitAgentStatus = (room, data) => {
  const io = Socket.getInstance();
  io.to(room).emit('message', data);
  return null;
};

/**
 * Listen to a socket event
 *
 * @param {string} event
 * @param {function} callback
 */
const onEvent = (event, callback) => {
  const io = Socket.getInstance();
  io.on(event, callback);
};

module.exports = {
  emitAgentStatus,
  onEvent,
};

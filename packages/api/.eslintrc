{"env": {"node": true, "es6": true}, "parserOptions": {"ecmaVersion": 2018, "sourceType": "module"}, "extends": ["eslint:recommended"], "plugins": ["promise", "hapi"], "rules": {"indent": ["error", 2], "quotes": ["error", "single"], "semi": ["error", "always"], "space-before-function-paren": ["error", "never"], "arrow-parens": ["error", "always"], "no-console": "error", "no-unused-vars": "error", "max-len": ["error", {"code": 100}], "no-var": "error", "hapi/hapi-capitalize-modules": "off"}}
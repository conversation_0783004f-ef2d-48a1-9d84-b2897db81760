{"name": "daiquiri-api", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "NODE_ENV=development nodemon start | pino-pretty", "test": "npm test", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "author": "", "license": "ISC", "dependencies": {"@hapi/boom": "^10.0.1", "@hapi/eslint-plugin": "^6.0.0", "@hapi/hapi": "^21.3.3", "@hapi/inert": "^7.1.0", "@hapi/vision": "^7.0.3", "axios": "^1.7.2", "dotenv": "^16.4.4", "eslint": "^8.56.0", "eslint-plugin-hapi": "^4.1.0", "eslint-plugin-promise": "^6.1.1", "hapi-api-version": "^2.3.1", "hapi-auth-jwt2": "^10.5.1", "hapi-pino": "^12.1.0", "hapi-query-builder": "^2.1.0", "hapi-swagger": "^17.2.1", "joi": "^17.13.1", "jwks-rsa": "^3.1.0", "lodash": "^4.17.21", "mongoose-paginate-v2": "^1.8.0", "mongoose-sequence": "^6.0.1", "nodemon": "^3.0.3", "pino-pretty": "^10.3.1", "socket.io": "^4.7.5"}}